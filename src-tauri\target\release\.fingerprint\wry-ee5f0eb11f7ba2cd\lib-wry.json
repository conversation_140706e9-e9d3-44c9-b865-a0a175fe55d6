{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 2040997289075261528, "path": 9546548099588661534, "deps": [[3007252114546291461, "tao", false, 700476586454723071], [3150220818285335163, "url", false, 2607936323075669519], [3540822385484940109, "windows_implement", false, 14512781935688964349], [3722963349756955755, "once_cell", false, 3786254663879612], [4381063397040571828, "webview2_com", false, 2109139913616277398], [4405182208873388884, "http", false, 12528391109576091438], [4684437522915235464, "libc", false, 13072182151999244151], [5986029879202738730, "log", false, 8837273349723292855], [7653476968652377684, "windows", false, 2672332482407547069], [8008191657135824715, "thiserror", false, 7441644336200258125], [8391357152270261188, "build_script_build", false, 15515121168484608409], [8569119365930580996, "serde_json", false, 11246661942346795939], [9689903380558560274, "serde", false, 16541609886822019218], [11989259058781683633, "dunce", false, 10411694973672158412]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wry-ee5f0eb11f7ba2cd\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}