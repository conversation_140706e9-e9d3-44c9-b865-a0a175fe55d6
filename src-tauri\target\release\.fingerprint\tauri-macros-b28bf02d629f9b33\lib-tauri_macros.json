{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 6413393700164640249, "deps": [[2713742371683562785, "syn", false, 11131448916814050216], [3060637413840920116, "proc_macro2", false, 6659999382514378454], [8292277814562636972, "tauri_utils", false, 16465916010854080396], [13077543566650298139, "heck", false, 3748740983335078662], [17492769205600034078, "tauri_codegen", false, 4886627712503442134], [17990358020177143287, "quote", false, 8260028736195906517]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-b28bf02d629f9b33\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}