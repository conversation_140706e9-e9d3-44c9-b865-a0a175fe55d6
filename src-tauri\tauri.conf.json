{"build": {"beforeDevCommand": "cd frontend && npm run dev", "beforeBuildCommand": "cd frontend && npm run build", "devPath": "http://127.0.0.1:3000", "distDir": "../frontend/dist", "withGlobalTauri": false}, "package": {"productName": "提肛小助手", "version": "1.0.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "process": {"all": false, "exit": true, "relaunch": true, "relaunchDangerousAllowSymlinkMacos": false}, "fs": {"all": false, "readFile": true, "writeFile": true, "readDir": true, "copyFile": true, "createDir": true, "removeDir": true, "removeFile": true, "renameFile": true, "exists": true}, "path": {"all": true}, "http": {"all": true, "request": true}}, "bundle": {"active": false, "identifier": "com.kegelhelper.app"}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "提肛小助手", "width": 1000, "height": 700, "minWidth": 800, "minHeight": 600}]}}