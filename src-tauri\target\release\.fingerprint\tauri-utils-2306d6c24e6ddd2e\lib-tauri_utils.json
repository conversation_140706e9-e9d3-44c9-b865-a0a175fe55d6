{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 3694897042393641924, "deps": [[561782849581144631, "html5ever", false, 15343300146065724864], [3150220818285335163, "url", false, 2607936323075669519], [3334271191048661305, "windows_version", false, 15232365920762273844], [4071963112282141418, "serde_with", false, 11876592231040366800], [4899080583175475170, "semver", false, 11231862191787307630], [5986029879202738730, "log", false, 8837273349723292855], [6262254372177975231, "kuchiki", false, 17116898004947416175], [6606131838865521726, "ctor", false, 3810745934542935350], [6997837210367702832, "infer", false, 10003073876284108651], [8008191657135824715, "thiserror", false, 7441644336200258125], [8569119365930580996, "serde_json", false, 11246661942346795939], [9689903380558560274, "serde", false, 16541609886822019218], [10301936376833819828, "json_patch", false, 13674334841495593483], [11989259058781683633, "dunce", false, 10411694973672158412], [14132538657330703225, "brotli", false, 5777855781570126963], [15622660310229662834, "walkdir", false, 10129955758969673011], [15932120279885307830, "memchr", false, 9278029316588931140], [17155886227862585100, "glob", false, 14199550071656753871], [17186037756130803222, "phf", false, 13479459993436032436]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-2306d6c24e6ddd2e\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}