{"rustc": 1842507548689473721, "features": "[\"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 1497995260721082616, "deps": [[4381063397040571828, "webview2_com", false, 2109139913616277398], [7653476968652377684, "windows", false, 2672332482407547069], [8292277814562636972, "tauri_utils", false, 8598246750322765673], [8319709847752024821, "uuid", false, 7522972156232273135], [8391357152270261188, "wry", false, 1082077227088715447], [11693073011723388840, "raw_window_handle", false, 3518983977318704076], [13208667028893622512, "rand", false, 5082811502463348362], [14162324460024849578, "tauri_runtime", false, 6059552405699082297], [16228250612241359704, "build_script_build", false, 6869356133244508889]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-33a57b2bbd50340d\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}